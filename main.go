package main

import (
	"errors"
	"fmt"
	"io"
	"log"
	"net"
	"strings"
)

func getLinesChanel(file io.ReadCloser) <-chan string {

	c := make(chan string)
	go func() {
		defer close(c)

		buffer := make([]byte, bufferSize)
		var currentLine strings.Builder

		for {
			n, err := file.Read(buffer)

			if err != nil {
				if errors.Is(err, io.EOF) {
					if currentLine.Len() > 0 {
						c <- currentLine.String()
					}
					break
				}
				panic(fmt.Sprintf("error reading file: %v bytes read, error: %v", n, err))
			}

			chunk := string(buffer[:n])
			parts := strings.Split(chunk, "\n")

			for i, part := range parts {
				if i > 0 {
					// print and reset on finding a newline
					c <- currentLine.String()
					currentLine.Reset()
				}
				currentLine.WriteString(part)
			}
		}

	}()
	return c
}

const bufferSize = 8

const port = ":40296"

func main() {
	listener, err := net.Listen("tcp", port)
	if err != nil {
		panic(fmt.Sprintf("could not create listener %v", err))
	}
	fmt.Printf("listening on %s \n", port)

	defer listener.Close()

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Fatalf("could not get a connection %v", err)
			return
		}
		fmt.Println("connection accepted from %")
		defer conn.Close()

		c := getLinesChanel(conn)
		for line := range c {
			fmt.Println(line)
		}
	}

}
