package main

import (
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"unicode/utf8"
)

// tod handle muti byte chars
func getLinesChanel(file io.ReadCloser) <-chan string {

	c := make(chan string)
	go func() {
		defer close(c)

		buffer := make([]byte, bufferSize)
		var currentLine strings.Builder
		var incompleteBytes []byte // Buffer for incomplete UTF-8 sequences

		for {
			n, err := file.Read(buffer)

			if err != nil {
				if errors.Is(err, io.EOF) {
					// Handle any remaining incomplete bytes
					if len(incompleteBytes) > 0 {
						currentLine.Write(incompleteBytes)
					}
					if currentLine.Len() > 0 {
						c <- currentLine.String()
					}
					break
				}
				panic(fmt.Sprintf("error reading file: %v bytes read, error: %v", n, err))
			}

			// Combine any incomplete bytes from previous read with new data
			fullBuffer := append(incompleteBytes, buffer[:n]...)

			// Find the last complete UTF-8 character
			validEnd := len(fullBuffer)
			for validEnd > 0 && !utf8.Valid(fullBuffer[:validEnd]) {
				validEnd--
			}

			// If we couldn't find any valid UTF-8, save all bytes for next iteration
			if validEnd == 0 {
				incompleteBytes = append(incompleteBytes, buffer[:n]...)
				continue
			}

			// Process the valid UTF-8 portion
			chunk := string(fullBuffer[:validEnd])

			// Save any remaining incomplete bytes for next iteration
			if validEnd < len(fullBuffer) {
				incompleteBytes = make([]byte, len(fullBuffer)-validEnd)
				copy(incompleteBytes, fullBuffer[validEnd:])
			} else {
				incompleteBytes = nil
			}

			parts := strings.Split(chunk, "\n")
			fmt.Printf("Debug buffer has %d parts\n", len(parts))

			for i, part := range parts {
				if i > 0 {
					// print and reset on finding a newline
					c <- currentLine.String()
					currentLine.Reset()
				}
				currentLine.WriteString(part)
			}
		}

	}()
	return c
}

const bufferSize = 64

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	c := getLinesChanel(file)

	for line := range c {
		fmt.Println("read:", line)
	}

}
