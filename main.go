package main

import (
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
)

//tod 
func getLinesChanel(file io.ReadCloser) <-chan string {

	c := make(chan string)
	go func() {
		defer close(c)

		buffer := make([]byte, bufferSize)
		var currentLine strings.Builder

		for {
			n, err := file.Read(buffer)

			if err != nil {
				if errors.Is(err, io.EOF) {
					if currentLine.Len() > 0 {
						c <- currentLine.String()
					}
					break
				}
				panic(fmt.Sprintf("error reading file: %v bytes read, error: %v", n, err))
			}

			chunk := string(buffer[:n])
			parts := strings.Split(chunk, "\n")
			fmt.Printf("Debug buffer has %d parts\n", len(parts))

			for i, part := range parts {
				if i > 0 {
					// print and reset on finding a newline
					c <- currentLine.String()
					currentLine.Reset()
				}
				currentLine.WriteString(part)
			}
		}

	}()
	return c
}

const bufferSize = 64

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	c := getLinesChanel(file)

	for line := range c {
		fmt.Println("read:", line)
	}

}
