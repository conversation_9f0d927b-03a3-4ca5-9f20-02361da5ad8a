package main

import (
	"bufio"
	"fmt"
	"io"
	"os"
)

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	buffer := make([]byte, 8)
	line := ""
	for {
		n, err := file.Read(buffer)

		if err != nil {
			if errors.Is(err, io.EOF) {
				if line != "" {
					fmt.Printf("done: %s\n", line)
				}
				break
			}
			panic(fmt.Sprintf("error reading file, %v , %v", n, err))
		}

		parts := strings.Split(string(buffer[:n]), "\n")

		for i := range parts {
			if i > 0 {
				fmt.Printf("read: %s\n", line)
				line = ""
			}
			line += parts[i]
		}
	}
}
