package main

import (
	"fmt"
	"io"
	"os"
)

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	for {
		b := make([]byte, 8)
		i, err := file.Read(b)
		if i == 0 {
			return
		}
		if err != nil {
			panic(fmt.Sprintf("error reading file, %v , %v", i, err))
		}

		fmt.Printf("%v: |%v|\n", i, (string(b[:i])))
	}

}
