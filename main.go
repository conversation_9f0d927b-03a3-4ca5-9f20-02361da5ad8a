package main

import (
	"fmt"
	"io"
	"os"
	"strings"
)

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	b := make([]byte, 8)
	line := ""
	for {
		i, err := file.Read(b)

		// Check error first - this is the correct pattern
		if err != nil {
			if err == io.EOF {
				break
			}
			panic(fmt.Sprintf("error reading file, %v , %v", i, err))
		}

		buff := string(b[:i])

		parts := strings.Split(buff, "\n")

		length := len(parts)

		for j := range parts {

			line = fmt.Sprintf("%s%s", line, parts[j])
			if length == 0 {
				continue
			}
			if j == length-1 {
				continue
			}
			fmt.Println(line)
			line = ""
		}

		//fmt.Print(string(b[:i]))
		//fmt.Printf("read: %s\n", (string(b[:i])))
	}

}
