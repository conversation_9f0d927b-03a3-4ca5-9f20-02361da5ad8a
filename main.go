package main

import (
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
)

func getLinesChanel(file io.ReadCloser) <-chan string {
	
	lines := ""

	go func() {
		
	}
	c := make(chan string)

	buffer := make([]byte, bufferSize)
	var currentLine strings.Builder

	for {
		n, err := file.Read(buffer)

		if err != nil {
			if errors.Is(err, io.EOF) {
				if currentLine.Len() > 0 {
					c <- currentLine.String()
				}
				break
			}
			panic(fmt.Sprintf("error reading file: %v bytes read, error: %v", n, err))
		}

		chunk := string(buffer[:n])
		parts := strings.Split(chunk, "\n")

		for i, part := range parts {
			if i > 0 {
				// We found a newline, so print the completed line
				c <- currentLine.String()
				currentLine.Reset()
			}
			currentLine.WriteString(part)
		}
	}

	return c
}

const bufferSize = 8

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	c := getLinesChanel(file)

	fmt.Printf("debug len %v", len(c))
	for line := range c {
		fmt.Println(line)
	}

}
