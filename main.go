package main

import (
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
)

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	const bufferSize = 8
	buffer := make([]byte, bufferSize)
	var currentLine strings.Builder

	for {
		n, err := file.Read(buffer)

		if err != nil {
			if errors.Is(err, io.EOF) {
				// Print any remaining content as the final line
				if currentLine.Len() > 0 {
					fmt.Printf("done: %s\n", currentLine.String())
				}
				break
			}
			panic(fmt.Sprintf("error reading file: %v bytes read, error: %v", n, err))
		}

		// Process the chunk we just read
		chunk := string(buffer[:n])
		parts := strings.Split(chunk, "\n")

		// Handle the parts
		for i, part := range parts {
			if i == 0 {
				// First part continues the current line
				currentLine.WriteString(part)
			} else {
				// We found a newline, so print the completed line
				fmt.Printf("read: %s\n", currentLine.String())
				currentLine.Reset()
				currentLine.WriteString(part)
			}
		}
	}

}
