package main

import (
	"fmt"
	"io"
	"os"
	"strings"
)

func main() {
	filename := "messages.txt"
	file, err := os.Open(filename)
	if err != nil {
		panic(fmt.Sprintf("could not open %v", filename))
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)

	// Read and print each line
	for scanner.Scan() {
		line := scanner.Text()
		fmt.Printf("read: %s\n", line)
	}

	// Check for scanning errors
	if err := scanner.Err(); err != nil {
		panic(fmt.Sprintf("error reading file: %v", err))
	}

	fmt.Println("done: EOF")
}
